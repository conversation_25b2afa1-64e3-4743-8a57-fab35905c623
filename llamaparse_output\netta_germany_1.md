
# Invoice

| Invoice no.:  | 001            |
| ------------- | -------------- |
| Invoice date: | Jul 13th, 2021 |
| Due:          | Feb 28th, 2022 |

## From
<PERSON><PERSON>pps

<PERSON>  
<EMAIL>  
5026697967  
saldoapps.com  
First str. 28-32, Chicago USA

## Bill to
Shepard corp.

<EMAIL>  
0026697967  
North str. 32, Chicago USA

| DESCRIPTION                                                                         | RATE, USD | QTY | TAX, % | DISC, % | AMOUNT, USD |
| ----------------------------------------------------------------------------------- | --------- | --- | ------ | ------- | ----------- |
| Prototype<br/>Prototype-based programming is a style of object-oriented programming | 50.00     | 24  | 10     | 5       | 2,200.00    |
| Design                                                                              | 50.00     | 120 | 10     | 5       | 6,000.00    |

## Payment Instruction

Paypal email  
<EMAIL>

Make checks payable to  
<PERSON>

Bank transfer  
Routing (ABA): *********

## Notes

Prototype-based programming is a style of object-oriented programming in which behaviour

| Subtotal:       | USD 8,200.00 |
| --------------- | ------------ |
| Discount (20%): | USD 0.00     |
| Shipping Cost:  | USD 0.00     |
| Sales Tax:      | USD 460.00   |
| Total:          | USD 8,660.00 |
| Amount paid:    | USD 0.00     |
| Balance Due:    | USD 8,660.00 |
