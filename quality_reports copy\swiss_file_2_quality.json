{"image_path": "expense_files\\swiss_file_2.png", "document_type": "receipt", "image_type_detection": {"is_digital_screenshot": "True", "confidence": 0.67, "image_subtype": "mobile_app_screenshot", "indicators": {"low_color_variety": false, "matches_common_resolution": false, "has_regular_edges": "True", "has_uniform_background": true, "has_many_straight_lines": true, "low_histogram_entropy": "True"}, "metadata": {"unique_colors": 374400, "edge_density": 0.037, "histogram_entropy": "5.48"}}, "timestamp": "2025-07-16T00:20:57.558239", "processing_time_seconds": 9.03, "overall_assessment": {"score": 80.0, "level": "Acceptable", "pass_fail": true, "issues_summary": ["Severe glare detected"], "recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "✅ Resolution quality is excellent for document processing.", "✅ Image sharpness is excellent.", "📷 Image is overexposed. Reduce camera exposure or lighting."]}, "detailed_results": {"resolution": {"dimensions": {"width": 1700, "height": 2200, "megapixels": 3.74}, "dpi": {"horizontal": 544.0, "vertical": 259.0, "average": 401.0}, "quality": {"score": 100.0, "level": "Digital Quality", "meets_ocr_requirements": true}, "aspect_ratio": {"actual": 0.77, "expected": 0.37, "deviation_percent": 110.2}, "recommendations": ["✅ Resolution quality is excellent for document processing."]}, "blur": {"metrics": {"laplacian_variance": 776.15, "is_blurry": false, "blur_score": 100.0, "blur_level": "Very Sharp"}, "motion_blur": {"detected": "False", "score": 5.899482085561497, "direction": "vertical"}, "focus_distribution": {"sharp_areas_percent": 35.0, "uniform_sharpness": false}, "recommendations": ["✅ Image sharpness is excellent."]}, "glare": {"exposure_metrics": {"mean_brightness": 204.9, "overexposed_percent": "44.46", "is_overexposed": "True", "contrast_ratio": 0.33}, "glare_analysis": {"glare_score": 0, "glare_level": "Severe", "num_glare_spots": 27, "glare_coverage_percent": 42.13, "glare_patterns": {"type": "flash", "description": "Camera flash reflection detected"}}, "affected_regions": [{"bbox": ["0", "0", "1700", "2200"], "center": [870, 1137], "area": "1559528", "intensity": 219.2015320855615}, {"bbox": ["589", "203", "50", "24"], "center": [610, 213], "area": "237", "intensity": 201.46333333333334}, {"bbox": ["678", "207", "15", "31"], "center": [684, 223], "area": "179", "intensity": 218.33763440860216}, {"bbox": ["698", "207", "42", "17"], "center": [718, 215], "area": "402", "intensity": 235.53361344537817}, {"bbox": ["1242", "212", "32", "40"], "center": [1258, 231], "area": "519", "intensity": 212.70859375}], "recommendations": ["📷 Image is overexposed. Reduce camera exposure or lighting.", "🔦 Disable camera flash to avoid reflections.", "⚠️ Significant glare detected. Adjust lighting and recapture."]}, "completeness": {"boundary_detected": true, "completeness_score": 100.0, "completeness_level": "Digital Document", "edge_analysis": {"edge_coverage": 100.0, "has_gaps": false, "num_gaps": 0}, "corner_analysis": {"visible_corners": 4, "missing_corners": [], "is_rectangular": true}, "issues": [], "boundary_points": [], "recommendations": ["✅ Digital screenshot - no physical boundaries to check."]}, "damage": {"damage_score": 100.0, "damage_level": "Digital (No Physical Damage)", "damage_types": [], "stain_analysis": {"count": 0, "coverage_percent": 0.0, "regions": []}, "tear_analysis": {"count": 0, "max_length": 0, "regions": []}, "fold_analysis": {"count": 0, "pattern": "none", "lines": []}, "recommendations": ["✅ Digital image - no physical damage possible."]}}, "score_breakdown": {"resolution": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "blur": {"score": 100.0, "weight": 0.25, "contribution": 25.0}, "glare": {"score": 0, "weight": 0.2, "contribution": 0.0}, "completeness": {"score": 100.0, "weight": 0.2, "contribution": 20.0}, "damage": {"score": 100.0, "weight": 0.15, "contribution": 15.0}}, "assessment_method": "opencv", "quality_passed": true, "quality_score": 80.0, "quality_level": "Acceptable", "main_issues": ["Severe glare detected"], "top_recommendations": ["📱 This is a mobile_app_screenshot - physical document checks have been adjusted.", "⚠️ Significant glare detected. Adjust lighting and recapture.", "✅ Resolution quality is excellent for document processing."]}