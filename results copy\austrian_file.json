{"source_file": "austrian_file.md", "processing_timestamp": "2025-07-15T16:14:49.152256", "dataset_metadata": {"filepath": "expense_files/austrian_file.png", "filename ": "austrian_file.png", "country": "Austria", "icp": "Global People", "dataset_file": "austrian_file.json"}, "classification_result": {"is_expense": true, "expense_type": "flights", "language": "German", "language_confidence": 95, "document_location": "Austria", "expected_location": "Austria", "location_match": true, "error_type": null, "error_message": null, "classification_confidence": 98, "reasoning": "The document contains travel-related expense information including a receipt/invoice indicator, flight details, booking code, passenger name, billing address in Slovakia, flight dates, and directions to retain the receipt for a flight. It matches the 'flights' expense category as it details airline travel. Language elements indicate German as primary, with high confidence due to format and vocabulary consistent with a German legal document. Document location is Austria as indicated by the presence of Austrian Airlines and confirmation with the expected location."}, "extraction_result": {"supplier_name": "Austrian Airlines AG", "supplier_address": "Office Park 2, A-1300 Vienna-Airport", "vat_number": "ATU15447707", "currency": null, "amount": null, "receipt_type": "Invoice", "receipt_quality": null, "personal_information": null, "business_trip_reporting": null, "travel_template": null, "manager_approval": null, "route_map": null, "kilometer_record": null, "car_details": null, "parking_documentation": null, "transaction_reference": "213000508057", "passenger_name": "FORISEK / MICHAL DR MR", "booking_code": "6GHMCV", "ticket_number": "257-2133783831", "billing_address": "Lubovnianska 14, 85107 Bratislava, Slovakia", "flight_data": [{"flight_number": "OS561", "date": "2013-08-31", "from": "Vienna Intl", "to": "Zurich", "departure_time": "07:20", "arrival_time": "08:45", "class": "Y", "baggage": "1 PC", "operated_by": "TYROLEAN AIRWAYS"}, {"flight_number": "OS568", "date": "2013-09-08", "from": "Zurich", "to": "Vienna Intl", "departure_time": "07:45", "arrival_time": "09:10", "class": "Y", "baggage": "1 PC", "operated_by": "TYROLEAN AIRWAYS"}]}, "compliance_result": {"validation_result": {"is_valid": false, "issues_count": 5, "issues": [{"issue_type": "Standards & Compliance | Fix Identified", "field": "<PERSON><PERSON><PERSON><PERSON>", "description": "The currency detail is missing which is a mandatory requirement for all receipts. Exchange rate details must also be clearly indicated.", "recommendation": "It is recommended to address this issue with the supplier or provider", "knowledge_base_reference": "Receipt currency must be the same currency with clear exchange rate specified."}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Amount", "description": "The amount is missing on the receipt which is a mandatory field for compliance.", "recommendation": "It is recommended to address this issue with the supplier or provider", "knowledge_base_reference": "Expense amount must be clearly stated on receipt."}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "Supplier Address", "description": "The supplier address is incorrect; does not match the required address format for Global People: Kärntner Ring 12, A-1010 Vienna, Austria.", "recommendation": "It is recommended to address this issue with the supplier or provider", "knowledge_base_reference": "Supplier Address must be K<PERSON>rntner Ring 12, A-1010 Vienna, Austria."}, {"issue_type": "Standards & Compliance | Fix Identified", "field": "VAT Number", "description": "The VAT number does not match the mandatory requirement for the company Global People IT-Services GmbH.", "recommendation": "It is recommended to address this issue with the supplier or provider", "knowledge_base_reference": "VAT identification number must be ATU77112189."}, {"issue_type": "Standards & Compliance | Follow-up Action Identified", "field": "Business Trip Reporting / Travel Template", "description": "The submission requires the Travel Expense Report Template Austria EUR.xlsx specific to travel receipts.", "recommendation": "Specify exact documentation requirements and procedures from knowledge base.", "knowledge_base_reference": "Submit separate report for each trip using specified template."}], "corrected_receipt": null, "compliance_summary": "Receipt is not compliant due to missing currency and amount fields, incorrect supplier address and VAT number, and lacking required travel documentation."}, "technical_details": {"content_type": "ReceiptValidationResult", "country": "Austria", "icp": "Global People", "receipt_type": "flights", "issues_count": 5, "has_reasoning": true}}, "processing_status": "completed", "uqlm_validation_available": true}